package com.feidi.xx.cross.common.enums.power;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运力 司机审核状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DrvAuditStatusEnum {

    // 0未申请 1申请中 2成功 3驳回
    NO("0", "未申请"),
    ING("1", "待审核"),
    SUCCESS("2", "审核通过"),
    REJECT("3", "审核驳回");

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static DrvAuditStatusEnum getByCode(String code) {
        for (DrvAuditStatusEnum itemEnum : DrvAuditStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DrvAuditStatusEnum itemEnum : DrvAuditStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

