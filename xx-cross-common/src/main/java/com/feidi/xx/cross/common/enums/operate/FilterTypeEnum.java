package com.feidi.xx.cross.common.enums.operate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 平台过滤类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FilterTypeEnum {
    // 0单号 1经纬度 2经纬度+时间

    /**
     * 单号
     */
    ORDER_NO("0", "单号"),

    /**
     * 经纬度
     */
    LAT_LON("1", "经纬度"),
    /**
     * 经纬度+时间
     */
    LAT_LON_TIME("2", "经纬度+时间");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static FilterTypeEnum getByCode(String code) {
        for (FilterTypeEnum itemEnum : FilterTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (FilterTypeEnum itemEnum : FilterTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

