package com.feidi.xx.cross.common.enums.finance;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 具体状态
 */
@Getter
@AllArgsConstructor
public enum FlowStatusEnum {

    FINISHED("FINISHED", "已完成", FlowShowStatusEnum.FINISHED, null, null),

    ORDER_FREEZE("ORDER_FREEZE", "处理中", FlowShowStatusEnum.PROCESSING, FlowSelectEnum.ORDER, FlowTypeEnum.ORDER_FREEZE_ADD),
    ORDER_REBATED("ORDER_REBATED", "已完成", FlowShowStatusEnum.FINISHED, FlowSelectEnum.ORDER, FlowTypeEnum.ORDER_ADD),
    ORDER_CLOSED("ORDER_CLOSED", "已关闭", FlowShowStatusEnum.CLOSED, FlowSelectEnum.ORDER, FlowTypeEnum.ORDER_CLOSED),

    //INVITE_ORDER_REWARD_FREEZE("INVITE_ORDER_REWARD_FREEZE", "待发放", FlowShowStatusEnum.PROCESSING, FlowSelectEnum.ORDER_INCOME, FlowTypeEnum.INVITE_ORDER_REWARD_FREEZE),
    INVITE_ORDER_REWARD("INVITE_ORDER_REWARD", "已发放", FlowShowStatusEnum.REWARD, FlowSelectEnum.INVITE, FlowTypeEnum.INVITE_ORDER_REWARD),
    COMPENSATION_REFUND("COMPENSATION_REFUND", "已扣款", FlowShowStatusEnum.FINED, FlowSelectEnum.INVITE, FlowTypeEnum.COMPENSATION_REFUND),

    REVIEW("REVIEW", "处理中", FlowShowStatusEnum.PROCESSING, FlowSelectEnum.CASH, FlowTypeEnum.CASHING),
    CASH_SUCCESS("CASH_SUCCESS", "已完成", FlowShowStatusEnum.FINISHED, FlowSelectEnum.CASH, FlowTypeEnum.CASH_SUB),
    CASH_FAIL("CASH_FAIL", "已失败", FlowShowStatusEnum.FAILED, FlowSelectEnum.CASH, FlowTypeEnum.CASH_FAIL),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 展示状态
     */
    private final FlowShowStatusEnum showStatus;

    /**
     * 选择类型
     */
    private final FlowSelectEnum selectType;

    /**
     * 流水类型，null的话取FxFlowSelectEnum的
     */
    private final FlowTypeEnum type;


    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static FlowStatusEnum getByCode(String code) {
        for (FlowStatusEnum itemEnum : FlowStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (FlowStatusEnum itemEnum : FlowStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    public static String getInfoByType(String type) {
        FlowTypeEnum flowType = FlowTypeEnum.getByCode(type);
        if (flowType != null) {
            for (FlowStatusEnum itemEnum : FlowStatusEnum.values()) {
                if (flowType.equals(itemEnum.getType())) {
                    return itemEnum.getInfo();
                }
            }
        }
        // 默认已完成
        return FINISHED.info;
    }

    /**
     * 是否是给FINISHED用的类型
     * 就是状态可变的都没有已完成，不变的才有已完成
     */
    public static boolean isTypeForFinished(FlowSelectEnum type) {
        List<FlowSelectEnum> list = Arrays.stream(FlowSelectEnum.values()).filter(e -> e.getType().size() > 1).toList();
        return !list.contains(type);
    }

    public static FlowStatusEnum getCorrectStatus(FlowSelectEnum type, FlowShowStatusEnum status) {
        List<FlowStatusEnum> list = Arrays.stream(values())
                .filter(e -> type.equals(e.getSelectType()) && status.equals(e.getShowStatus()))
                .toList();
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return FINISHED;
    }

}

