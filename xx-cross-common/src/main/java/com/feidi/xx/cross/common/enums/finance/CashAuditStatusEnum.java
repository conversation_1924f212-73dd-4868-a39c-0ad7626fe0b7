package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 财务 体现审核状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CashAuditStatusEnum {

    // 0申请中 1.成功 2驳回
    ING("0", "申请中"),
    SUCCESS("1", "成功"),
    REJECT("2", "驳回");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static CashAuditStatusEnum getByCode(String code) {
        for (CashAuditStatusEnum itemEnum : CashAuditStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (CashAuditStatusEnum itemEnum : CashAuditStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

