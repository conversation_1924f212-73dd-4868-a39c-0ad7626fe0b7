
package com.feidi.xx.cross.common.constant.order;

/**
 * 上报引擎层服务 RocketMQ 常量类
 * <p>
 * 作者：zyt
 * 开发时间：2024-07-14
 * <AUTHOR>
 */
public final class OrderRocketMQConstant {

    /**
     * 订单邀请有奖延迟队列 Topic Key
     */
    public static final String XX_ORDER_INVITE_REWARD_DELAY_TOPIC = "xx-order-invite-reward-delay-topic${unique-name:}";

    public static final String XX_ORDER_INVITE_REWARD_DELAY_CG_KEY = "xx-order-invite-reward-delay-cg${unique-name:}";

    public static final String XX_ORDER_OPERATE_CG_KEY = "xx-cross-order-operate-cg${unique-name:}";

    /**
     * 订单操作记录 Topic Key
     */
    public static final String XX_ORDER_OPERATE_TOPIC_KEY = "xx-cross-order-operate-topic${unique-name:}";

    /**
     * 订单组 cg
     */
    public static final String XX_ORDER_ROB_CG_KEY = "xx-cross-order-rob-cg${unique-name:}";

    /**
     * 订单自动抢单 Topic Key
     */
    public static final String XX_ORDER_ROB_TOPIC_KEY = "xx-cross-order-rob-topic${unique-name:}";

    /**
     * 订单自动抢单 Topic Key
     */
    public static final String XX_ORDER_MT_VIRTUAL_PHONE_TOPIC_KEY = "xx-cross-order-mt-virtual-phone-topic${unique-name:}";

    /**
     * 订单自动抢单 Topic cg
     */
    public static final String XX_ORDER_MT_VIRTUAL_PHONE_GC_KEY = "xx-cross-order-mt-virtual-phone-cg${unique-name:}";

    /**
     * 订单取消处理 Topic Key
     */
    public static final String XX_ORDER_CANCEL_TOPIC_KEY = "xx-cross-order-cancel-topic${unique-name:}";

    /**
     * 订单取消处理组 cg
     */
    public static final String XX_ORDER_CANCEL_CG_KEY = "xx-cross-order-cancel-cg${unique-name:}";


    /**
     * 订单状态变更消息 Topic Key
     */
    public static final String XX_ORDER_STATUS_CHANGE_TOPIC_KEY = "xx-cross-order-status-change-topic${unique-name:}";

}
