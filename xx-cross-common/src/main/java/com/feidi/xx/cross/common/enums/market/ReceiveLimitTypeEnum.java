package com.feidi.xx.cross.common.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 社群活动领取限制类型枚举
 * 领取限制 1:活动期间限领一次 2:每天限领一次
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReceiveLimitTypeEnum {

    ONCE_PER_CAMPAIGN("1", "活动期间限领一次"),
    ONCE_PER_DAY("2", "活动期间每天限领一次");

    private final String code;
    private final String info;

    /**
     * 根据 code 获取枚举
     */
    public static ReceiveLimitTypeEnum fromCode(String code) {
        for (ReceiveLimitTypeEnum e : values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
