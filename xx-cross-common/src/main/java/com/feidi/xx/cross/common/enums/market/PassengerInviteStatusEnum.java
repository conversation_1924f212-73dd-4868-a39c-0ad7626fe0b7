package com.feidi.xx.cross.common.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 乘客邀请状态(1已注册,2已首单)
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PassengerInviteStatusEnum {
    /**
     * 已注册
     */
    REGISTERED("1", "已注册"),
    /**
     * 已首单
     */
    FIRST_ORDER("2", "已首单");

    private final String code;

    private final String info;

    /**
     * 获取枚举值
     *
     * @param code 枚举值
     * @return 枚举值
     */
    public static PassengerInviteStatusEnum getByCode(String code) {
        for (PassengerInviteStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
