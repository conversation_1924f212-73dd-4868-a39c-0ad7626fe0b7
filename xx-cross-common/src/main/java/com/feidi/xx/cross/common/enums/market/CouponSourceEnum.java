package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 优惠卷状态
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum CouponSourceEnum {

    AGENT("0", "代理商券"),
    PLATFORM("1", "平台券");
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (CouponSourceEnum itemEnum : CouponSourceEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

