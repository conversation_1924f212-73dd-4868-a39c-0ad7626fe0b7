package com.feidi.xx.cross.enums.finance;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.feidi.xx.cross.common.constant.finance.FinanceCacheConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum FinCacheKeyEnum {


    /**---------------------------         财务管理模块          ---------------------------------**/
    FX_CASH_APPLY_KEY(FinanceCacheConstants.FX_CASH_APPLY_KEY, 1, "见静态常量定义，参数1.司机id"),
    FX_TRANSFER_APPLY_KEY(FinanceCacheConstants.FX_TRANSFER_APPLY_KEY,  1, "见静态常量定义，参数1.司机id"),
    FX_PASSWORD_WRONG_TIMES_KEY(FinanceCacheConstants.FX_PASSWORD_WRONG_TIMES_KEY,  1, "见静态常量定义，参数1.司机id"),
    ;

    /**
     * 前缀
     */
    private final String prefix;

    /**
     * 参数个数
     */
    private final int argsNum;

    /**
     * 描述 需要描述每一个参数都应该传什么
     */
    private final String desc;

    /**
     * 分隔符
     */
    private static final String SEPARATOR = ":";

    static {
        Set<String> set = Arrays.stream(values()).map(FinCacheKeyEnum::getPrefix).collect(Collectors.toSet());
        Assert.isTrue(values().length == set.size(), "存在部分枚举的prefix重复，无法初始化");
    }

    public String create(Object... args) {
        if (argsNum == 0) {
            return prefix;
        }
        Assert.isTrue(args.length == argsNum, "参数个数不正确");
        return prefix + StrUtil.join(SEPARATOR, args);
    }

}
