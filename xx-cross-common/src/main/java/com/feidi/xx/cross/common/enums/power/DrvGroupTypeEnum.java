package com.feidi.xx.cross.common.enums.power;

import com.feidi.xx.cross.common.enums.order.CancelTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DrvGroupTypeEnum {

    JOIN("0", "加盟"),

    SELF("1", "自营"),
    ;


    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static DrvGroupTypeEnum getByCode(String code) {
        for (DrvGroupTypeEnum itemEnum : DrvGroupTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DrvGroupTypeEnum itemEnum : DrvGroupTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

}
