package com.feidi.xx.cross.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 金额单位转换工具类：元 <-> 分（使用 movePointRight / movePointLeft 简化）
 * 移位和multiply 的区别就是移位不丢失精度
 */
public class MoneyUtil {

    private static final int SCALE = 2; // 保留小数点位数

    /**
     * 元转分（BigDecimal → long）
     * eg: 12.34 元 → 1234 分
     */
    public static long yuanToFen(BigDecimal amountYuan) {
        if (amountYuan == null) return 0L;
        return amountYuan
                .movePointRight(2) // 相当于 ×100
                .setScale(0, RoundingMode.HALF_UP) // 四舍五入取整
                .longValue();
    }

    /**
     * 分转元（long → BigDecimal）
     * eg: 1234 分 → 12.34 元
     */
    public static BigDecimal fenToYuan(long amountFen) {
        return BigDecimal.valueOf(amountFen)
                .movePointLeft(2) // 相当于 ÷100
                .setScale(SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 格式化元金额（带千分位，¥12,345.67）
     */
    public static String formatYuan(BigDecimal amountYuan) {
        if (amountYuan == null) return "¥0.00";
        DecimalFormat df = new DecimalFormat("¥#,##0.00");
        return df.format(amountYuan);
    }

    /**
     * 格式化分金额（先转元再格式化）
     */
    public static String formatFen(long amountFen) {
        return formatYuan(fenToYuan(amountFen));
    }

    // 测试
    public static void main(String[] args) {
        BigDecimal yuan = new BigDecimal("12.3456");
        long fen = yuanToFen(yuan); // 1235
        System.out.println("元转分: " + fen);

        BigDecimal yuan2 = fenToYuan(fen); // 12.35
        System.out.println("分转元: " + yuan2);

        System.out.println("格式化金额: " + formatFen(12345678)); // ¥123,456.78
    }
}
