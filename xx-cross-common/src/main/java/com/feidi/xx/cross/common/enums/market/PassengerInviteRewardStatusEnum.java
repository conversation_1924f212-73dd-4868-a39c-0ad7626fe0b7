package com.feidi.xx.cross.common.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发放状态(1已发放,2发放失败,3客诉退券,4,客诉退券失败)
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum PassengerInviteRewardStatusEnum {

    /**
     * 已发放
     */
    GRANTED("1", "已发放"),
    /**
     * 发放失败
     */
    GRANT_FAILED("2", "发放失败"),
    /**
     * 客诉退券
     */
    REFUND("3", "客诉退券"),
    /**
     * 客诉退券失败
     */
    REFUND_FAILED("4", "客诉退券失败"),
    ;
    private final String code;

    private final String info;

    /**
     * 根据编码获取枚举
     */
    public static PassengerInviteRewardStatusEnum getByCode(String code) {
        for (PassengerInviteRewardStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
