package com.feidi.xx.cross.common.enums.platform;

import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 哈啰订单状态枚举
 *
 * <AUTHOR>
 * @date 2024/9/26
 */
@Getter
@AllArgsConstructor
public enum HbkOrderStatusEnum {

    CANCEL(-1, "已取消", OrderStatusEnum.CANCEL.getCode()),
    CREATE(10, "待车主接单", OrderStatusEnum.CREATE.getCode()),
    PAY(20, "待支付", OrderStatusEnum.RECEIVE.getCode()),
    PICK(30, "待车主到达", OrderStatusEnum.PICK.getCode()),
    PICK_START(40, "待上车", OrderStatusEnum.PICK_START.getCode()),
    ING(50, "行程中", OrderStatusEnum.ING.getCode()),
    FINISH(60, "到达乘客目的地", OrderStatusEnum.FINISH.getCode()),
    ;

    private final int code;
    private final String info;
    private final String orderStatus;

    public static String getInfoByCode(Integer code) {
        for (HbkOrderStatusEnum itemEnum : HbkOrderStatusEnum.values()) {
            if (itemEnum.getCode() == code) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    /**
     * 通过code获取订单状态
     *
     * @param code
     * @return
     */
    public static String getOrderStatusByCode(Integer code) {
        for (HbkOrderStatusEnum itemEnum : HbkOrderStatusEnum.values()) {
            if (itemEnum.getCode() == code) {
                return itemEnum.getOrderStatus();
            }
        }
        return null;
    }

}
