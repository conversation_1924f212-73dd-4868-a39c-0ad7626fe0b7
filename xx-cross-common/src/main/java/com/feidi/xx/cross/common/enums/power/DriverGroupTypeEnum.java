package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 司机组类型枚举
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Getter
@AllArgsConstructor
public enum DriverGroupTypeEnum {

    PARTNER("0", "加盟"),

    SELF("1", "自营"),
    ;


    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static DriverGroupTypeEnum getByCode(String code) {
        for (DriverGroupTypeEnum itemEnum : DriverGroupTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DriverGroupTypeEnum itemEnum : DriverGroupTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

}
