package com.feidi.xx.cross.common.enums.passenger;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 乘客服务告知函同意状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AgreementStatus {

    NOT_AGREED("0", "未同意"),
    AGREED("1", "已同意");

    private final String code;
    private final String description;

    /**
     * 根据 char 值获取对应的枚举
     */
    public static AgreementStatus fromCode(String code) {
        for (AgreementStatus status : values()) {
            if (Objects.equals(status.code, code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的 AgreementStatus code: " + code);
    }

    /**
     * 判断是否已同意
     */
    public boolean isAgreed() {
        return this == AGREED;
    }
}