package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 活动对象[司机|乘客]
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum ActivityColonyEnum {

    DRIVER(UserTypeEnum.DRIVER_USER.getUserType(), UserTypeEnum.DRIVER_USER.getInfo()),
    PASSENGER(UserTypeEnum.PASSENGER_USER.getUserType(), UserTypeEnum.PASSENGER_USER.getInfo()),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (ActivityColonyEnum itemEnum : ActivityColonyEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

