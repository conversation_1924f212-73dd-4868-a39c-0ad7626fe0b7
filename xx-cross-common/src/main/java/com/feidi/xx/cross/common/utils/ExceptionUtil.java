package com.feidi.xx.cross.common.utils;

import com.feidi.xx.common.core.exception.ServiceException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * 异常工具类
 */
@Slf4j
@UtilityClass
public class ExceptionUtil {

    public static void ignoreEx(Runnable handle) {
        try {
            handle.run();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void ignoreEx(Runnable handle, String msg) {
        try {
            handle.run();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(msg);
        }
    }

    public static void ignoreEx(Runnable handle, String msg, Runnable fin) {
        try {
            handle.run();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error(msg);
        } finally {
            fin.run();
        }
    }

    public static void throwEx(Runnable handle) {
        try {
            handle.run();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    public static void throwEx(Runnable handle, String msg) {
        try {
            handle.run();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(msg);
        }
    }

    public static void throwEx(Runnable handle, String msg, Runnable fin) {
        try {
            handle.run();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(msg);
        } finally {
            fin.run();
        }
    }

}
