package com.feidi.xx.cross.common.enums.operate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 区域类型模块枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AreaTypeEnum {

    CITY("0", "城市"),
    COUNTY("1", "区县");

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static AreaTypeEnum getByCode(String code) {
        for (AreaTypeEnum itemEnum : AreaTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (AreaTypeEnum itemEnum : AreaTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

