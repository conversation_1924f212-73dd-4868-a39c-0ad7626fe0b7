package com.feidi.xx.cross.common.enums.operate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日期类型模块枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum HolidayTypeEnum {

    WEEKDAY_PRICE("0", "工作日价格"),
    HOLIDAY_PRICE("1", "节假日价格");

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static HolidayTypeEnum getByCode(String code) {
        for (HolidayTypeEnum itemEnum : HolidayTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (HolidayTypeEnum itemEnum : HolidayTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

