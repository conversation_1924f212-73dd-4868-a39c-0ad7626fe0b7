package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运力 司机证件类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DriverIdentityEnum {

    NO("0", "未申请", "Y"),
    CROSS_DRV("1", "顺风车司机", "Y"),
    CITY_DRV("2", "市内司机", "N");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 描述
     */
    private final String isShow;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static DriverIdentityEnum getByCode(String code) {
        for (DriverIdentityEnum itemEnum : DriverIdentityEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DriverIdentityEnum itemEnum : DriverIdentityEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

