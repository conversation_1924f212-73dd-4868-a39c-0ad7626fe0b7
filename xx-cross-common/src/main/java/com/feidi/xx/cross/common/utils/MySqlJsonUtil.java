package com.feidi.xx.cross.common.utils;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import lombok.experimental.UtilityClass;

import java.util.Collection;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * MySQL 8 JSON 类型字段操作工具类
 * 支持 JSON_OVERLAPS 和 JSON_CONTAINS 等 JSON 函数操作
 * JSON_CONTAINS 函数 用于判断 JSON 类型字段是否包含指定的值
 * JSON_OVERLAPS 函数 用于判断 JSON 类型字段是否与指定的 JSON 数组重叠
 *
 * <AUTHOR>
 */
@UtilityClass
public class MySqlJsonUtil {

    /**
     * 处理参数,占位符
     *
     * @param value 值
     * @param idx   索引
     * @return
     */
    public static String processArg(Object value, int idx) {
        if (value instanceof Number || value instanceof Boolean) {
            return String.format("CAST({%s} AS JSON)", idx);
        } else if (value instanceof String) {
            return "JSON_QUOTE({" + idx + "})";
        } else if (value instanceof Collection<?> values) {
            Object[] array = values.toArray();
            return IntStream.range(0, values.size())
                    .mapToObj(i -> processArg(array[i], i + idx))
                    .collect(Collectors.joining(",", "JSON_ARRAY(", ")"));
        }
        throw new RuntimeException("不支持的类型: " + value.getClass().getName());
    }


    /**
     * JSON_CONTAINS 函数 用于判断 JSON 类型字段是否包含指定的值
     *
     * @param wrapper 包装器
     * @param column  列名
     * @param value   值
     */
    public static void jsonContains(AbstractWrapper<?, ?, ?> wrapper, String column, Object value) {
        if (value == null) {
            return;
        }
        String funcSql = String.format("JSON_CONTAINS(%s, %s)", column, processArg(value, 0));
        if (value instanceof Collection<?>) {
            wrapper.apply(true, funcSql, ((Collection<?>) value).toArray());
        } else {
            wrapper.apply(true, funcSql, value);
        }
    }

    /**
     * JSON_CONTAINS 函数 用于判断 JSON 类型字段是否包含指定的值
     *
     * @param wrapper 包装器
     * @param column  列名
     * @param path    路径 "$.xxx" jsonPath语法
     * @param value   值
     */
    public static void jsonContains(AbstractWrapper<?, ?, ?> wrapper, String column, String path, Object value) {
        if (value == null) {
            return;
        }
        String funcSql = String.format("JSON_CONTAINS(%s, %s, '%s')", column, processArg(value, 0), path);
        if (value instanceof Collection<?>) {
            wrapper.apply(true, funcSql, ((Collection<?>) value).toArray());
        } else {
            wrapper.apply(true, funcSql, value);
        }
    }

    /**
     * JSON_OVERLAPS 函数 用于判断 JSON 类型字段是否与指定的 JSON 数组重叠
     *
     * @param wrapper 包装器
     * @param column  列名
     * @param values  值
     */
    public static void jsonOverlaps(AbstractWrapper<?, ?, ?> wrapper, String column, Collection<?> values) {
        if (CollUtil.isEmpty(values)) {
            return;
        }
        String qStr = IntStream.range(0, values.size()).mapToObj(value -> "{" + value + "}")
                .collect(Collectors.joining(",", "JSON_ARRAY(", ")"));

        String funcSql = String.format("JSON_OVERLAPS(%s, %s)", column, qStr);
        wrapper.apply(true, funcSql, values.toArray());
    }

    /**
     * JSON_OVERLAPS 函数 用于判断 JSON 类型字段是否与指定的 JSON 数组重叠
     *
     * @param wrapper 包装器
     * @param column  列名
     * @param path    路径 "$.xxx"  jsonPath语法
     * @param values  值
     */
    public static void jsonOverlaps(AbstractWrapper<?, ?, ?> wrapper, String column, String path, Collection<?> values) {
        if (CollUtil.isEmpty(values)) {
            return;
        }
        String qStr = IntStream.range(0, values.size()).mapToObj(value -> "{" + value + "}")
                .collect(Collectors.joining(",", "JSON_ARRAY(", ")"));

        if (path != null) {
            column = String.format("%s->'%s'", column, path);
        }
        String funcSql = String.format("JSON_OVERLAPS(%s, %s)", column, qStr);
        wrapper.apply(true, funcSql, values.toArray());
    }
}