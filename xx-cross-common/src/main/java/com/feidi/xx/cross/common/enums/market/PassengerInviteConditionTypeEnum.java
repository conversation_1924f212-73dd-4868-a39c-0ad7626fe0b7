package com.feidi.xx.cross.common.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发放条件(1注册成功,2完成首单)
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PassengerInviteConditionTypeEnum {

    REGISTER("1", "注册成功", "邀请注册"),
    FIRST_ORDER("2", "完成首单", "首单完单"),
    ;
    private final String code;

    private final String info;

    private final String text;


    /**
     * 获取枚举值
     *
     * @param code 枚举值
     * @return 枚举值
     */
    public static PassengerInviteConditionTypeEnum getByCode(String code) {
        for (PassengerInviteConditionTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static PassengerInviteConditionTypeEnum getByCodeThrow(String inviteConditionType) {
        PassengerInviteConditionTypeEnum value = getByCode(inviteConditionType);
        if (value == null) {
            throw new IllegalArgumentException("发放条件不正确");
        }
        return value;
    }
}
