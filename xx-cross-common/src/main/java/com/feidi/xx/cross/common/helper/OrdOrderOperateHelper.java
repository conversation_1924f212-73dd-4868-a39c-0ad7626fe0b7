package com.feidi.xx.cross.common.helper;

import cn.dev33.satoken.stp.StpUtil;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.TraceIdUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

/**
 * 订单操作记录辅助类
 */
@Slf4j
@UtilityClass
public class OrdOrderOperateHelper {

    /**
     * 构建已登录用户的订单操作记录
     *
     * @param orderId     订单id
     * @param operateType 操作类型
     * @return 订单操作记录对象
     * @return 订单操作记录对象
     */
    public static OrdOrderOperateEvent buildOrderOperateEvent(Long orderId, String operateType, String paramsJson) {
        String userType = UserTypeEnum.AUTO_USER.getUserType();
        Long userId = 0L;
        if (StpUtil.isLogin()) {
            userType = LoginHelper.getUserType().getUserType();
            userId = LoginHelper.getUserId();
        }
        return buildOrderOperateEvent(orderId, operateType, paramsJson, userType, userId);
    }

    /**
     * 构建美团平台订单操作记录
     *
     * @param orderId     订单id
     * @param operateType 操作类型
     * @return 订单操作记录对象
     */
    public static OrdOrderOperateEvent buildMtOrderOperateEvent(Long orderId, String operateType, String paramsJson) {
        return buildOrderOperateEvent(orderId, operateType, paramsJson, UserTypeEnum.MT.getUserType(), 0L);
    }

    /**
     * 构建平台订单操作记录
     *
     * @param orderId     订单id
     * @param operateType 操作类型
     * @param userType    用户类型
     * @param userId      用户id
     * @return 订单操作记录对象
     */
    public static OrdOrderOperateEvent buildOrderOperateEvent(Long orderId, String operateType, String paramsJson, String userType, Long userId) {
        return OrdOrderOperateEvent.builder()
                .traceId(MDC.get(TraceIdUtils.TRACE_ID))
                .tenantId(LoginHelper.getTenantId())
                .orderId(orderId)
                .userType(userType)
                .userId(userId)
                .operateType(operateType)
                .paramsJson(paramsJson)
                .status(SuccessFailEnum.SUCCESS.getCode())
                .build();
    }

    /**
     * 构建投保日志记录
     * @param tenantId 租户id
     * @param orderId 订单id
     * @param driverId 司机id
     * @param xmlStr xml字符串
     * @return
     */
    public static OrdOrderOperateEvent buildInsureRecordEvent(String tenantId, Long orderId, String operateType, String paramsJson) {
        return OrdOrderOperateEvent.builder()
                .tenantId(tenantId)
                .orderId(orderId)
                .operateType(operateType)
                .userType(UserTypeEnum.AC_INSURE.getUserType())
                .userId(0L)
                .timeStamp(DateUtils.getUnixTimeStamps())
                .paramsJson(paramsJson)
                .status(SuccessFailEnum.FAIL.getCode())
                .build();

    }

    /**
     * 订单操作日志记录
     * @param tenantId 租户id
     * @param orderId 订单id
     * @param operateType  操作类型
     * @param xmlStr xml字符串
     * @return
     */
    public static OrdOrderOperateEvent buildOrderOperateEvent(String tenantId,Long orderId,String operateType,String xmlStr) {
        return OrdOrderOperateEvent.builder()
                .tenantId(tenantId)
                .orderId(orderId)
                .userType(UserTypeEnum.AUTO_USER.getUserType())
                .userId(0L)
                .timeStamp(DateUtils.getUnixTimeStamps())
                .operateType(operateType)
                .paramsJson(xmlStr)
                .status(SuccessFailEnum.FAIL.getCode())
                .build();

    }
}
