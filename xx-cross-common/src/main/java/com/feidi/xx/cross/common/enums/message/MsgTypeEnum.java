package com.feidi.xx.cross.common.enums.message;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息类型
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum MsgTypeEnum {

    // 1：文本 2：图片 3：语音 4：视频 5：位置 6：文件 7：自定义消息
    TEXT("1", "文本"),
    PIC("2", "图片"),
    AUDIO("3", "语音"),
    VIDEO("4", "视频"),
    POSITION("5", "位置"),
    FILE("6", "文件"),
    DIY("7", "自定义消息"),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static MsgTypeEnum getByCode(String code) {
        for (MsgTypeEnum itemEnum : MsgTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (MsgTypeEnum itemEnum : MsgTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

