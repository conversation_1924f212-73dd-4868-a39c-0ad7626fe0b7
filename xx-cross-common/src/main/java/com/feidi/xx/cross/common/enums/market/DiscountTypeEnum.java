package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 折扣类型类型
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum DiscountTypeEnum {

    DEDUCTION("0", "立减券"),
    DISCOUNT("1", "满减券"),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DiscountTypeEnum itemEnum : DiscountTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    public static DiscountTypeEnum getByCode(String code) {
        return Arrays.stream(DiscountTypeEnum.values())
                .filter(itemEnum -> itemEnum.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}

