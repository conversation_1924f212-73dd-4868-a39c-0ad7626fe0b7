package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运力 司机证件类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DateIntervalEnum {

    TODAY("0", "实时"),
    YESTERDAY("1", "昨天"),
    WEE<PERSON>("2", "七天内"),
    MONTH("3", "一月内"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;


    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static DateIntervalEnum getByCode(String code) {
        for (DateIntervalEnum itemEnum : DateIntervalEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DateIntervalEnum itemEnum : DateIntervalEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

