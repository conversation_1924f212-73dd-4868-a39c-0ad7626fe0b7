package com.feidi.xx.cross.common.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 社群活动状态枚举
 *活动状态 0:待开始 1:进行中 2:已结束
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CampaignStatusEnum {

    NOT_STARTED("0", "待开始"),
    ONGOING("1", "进行中"),
    ENDED("2", "已结束");

    private final String code;
    private final String info;

    /**
     * 根据 code 获取枚举
     */
    public static CampaignStatusEnum fromCode(String code) {
        for (CampaignStatusEnum e : values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static CampaignStatusEnum getByCodeThrow(String code) {
        CampaignStatusEnum statusEnum = fromCode(code);
        if (statusEnum == null) {
            throw new IllegalArgumentException("非法的活动状态: " + code);
        }
        return statusEnum;
    }
}
