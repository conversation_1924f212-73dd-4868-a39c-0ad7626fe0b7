package com.feidi.xx.cross.common.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 上下架状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ShelvesStatusEnum {

    ONLINE("1", "上架"),
    OFFLINE("0", "下架");

    private final String code;
    private final String info;

    public static ShelvesStatusEnum fromCode(String code) {
        for (ShelvesStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}