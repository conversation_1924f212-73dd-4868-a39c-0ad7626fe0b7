package com.feidi.xx.cross.common.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 城际 保险记录类型枚举
 *
 * <AUTHOR>
 * @date 2024/10/08
 */
@Getter
@AllArgsConstructor
public enum InsureRecordTypeEnum {

    INSURE("1", "投保", "0101"),
    CANCEL_INSURE("2", "取消投保", "0102"),
    INSURE_CALLBACK("3", "保险回调", "0201"),
    CANCEL_INSURE_CALLBACK("4", "取消投保回调", "0202"),
    ;


    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    private final String fileType;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static InsureRecordTypeEnum getByCode(String code) {
        for (InsureRecordTypeEnum itemEnum : InsureRecordTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param fileType
     * @return
     */
    public static String getInfoByFileType(String fileType) {
        for (InsureRecordTypeEnum itemEnum : InsureRecordTypeEnum.values()) {
            if (itemEnum.getFileType().equals(fileType)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    public static String getCodeByFileType(String fileType) {
        for (InsureRecordTypeEnum itemEnum : InsureRecordTypeEnum.values()) {
            if (itemEnum.getFileType().equals(fileType)) {
                return itemEnum.getCode();
            }
        }
        return null;
    }
}

