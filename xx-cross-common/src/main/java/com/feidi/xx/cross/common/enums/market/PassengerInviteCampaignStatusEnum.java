package com.feidi.xx.cross.common.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 乘客推乘客活动状态枚举
 * 0:待开始 1:进行中 2:已结束
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PassengerInviteCampaignStatusEnum {
    /**
     * 待开始
     */
    TO_BE_STARTED("0", "待开始"),
    /**
     * 进行中
     */
    ONGOING("1", "进行中"),
    /**
     * 已结束
     */
    ENDED("2", "已结束");

    private final String code;

    private final String info;

    public static PassengerInviteCampaignStatusEnum getByCode(String code) {
        for (PassengerInviteCampaignStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取枚举值，若不存在则抛出异常
     *
     * @param code 状态码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果状态码不存在
     */
    public static PassengerInviteCampaignStatusEnum getByCodeThrow(String code) {
        PassengerInviteCampaignStatusEnum statusEnum = getByCode(code);
        if (statusEnum == null) {
            throw new IllegalArgumentException("无效的活动状态");
        }
        return statusEnum;
    }
}
