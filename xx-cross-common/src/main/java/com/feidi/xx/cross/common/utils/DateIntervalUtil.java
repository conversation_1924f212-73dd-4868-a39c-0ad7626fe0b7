package com.feidi.xx.cross.common.utils;

import cn.hutool.core.date.DateUtil;
import com.feidi.xx.cross.common.enums.power.DateIntervalEnum;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DateIntervalUtil {
    /**
     * 根据时隔获取日期范围(结束时间今天)
     * @param interval interval
     * @return 日期范围
     */
    public static List<Date> getDateRangeByInterval(String interval){
        Date startTime, endTime, today = new Date();
        if (DateIntervalEnum.YESTERDAY.getCode().equals(interval)) {
            today = DateUtil.offsetDay(today, -1);
            startTime = DateUtil.beginOfDay(today);
            endTime = DateUtil.endOfDay(today);
        } else if (DateIntervalEnum.WEEK.getCode().equals(interval)) {
            startTime = DateUtil.offsetDay(today, -7);
            startTime = DateUtil.beginOfDay(startTime);
            endTime = DateUtil.endOfDay(today);
        } else if (DateIntervalEnum.MONTH.getCode().equals(interval)) {
            startTime = DateUtil.offsetDay(today, -30);
            startTime = DateUtil.beginOfDay(startTime);
            endTime = DateUtil.endOfDay(today);
        } else {
            //默认今日
            startTime = DateUtil.beginOfDay(today);
            endTime = DateUtil.endOfDay(today);
        }
        return List.of(startTime, endTime);
    }
}
