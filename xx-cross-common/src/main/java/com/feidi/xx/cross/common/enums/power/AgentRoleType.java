package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 司机组类型枚举
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Getter
@AllArgsConstructor
public enum AgentRoleType {

    ADMIN("0", "管理员"),

    DISPATCH("1", "调度"),
    FINANCE("2", "财务"),
    ;


    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static AgentRoleType getByCode(String code) {
        for (AgentRoleType itemEnum : AgentRoleType.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (AgentRoleType itemEnum : AgentRoleType.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

}
