package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;


@Getter
@AllArgsConstructor
public enum FlowSelectEnum {

    CASH("CASH", "余额提现", Arrays.asList(FlowTypeEnum.CASHING, FlowTypeEnum.CASH_SUB, FlowTypeEnum.CASH_FAIL)),
    CASH_FAIL("CASH_FAIL", "提现失败退款", Arrays.asList(FlowTypeEnum.CASH_ADD)),
    ORDER("ORDER", "订单分佣", Arrays.asList(FlowTypeEnum.ORDER_FREEZE_ADD, FlowTypeEnum.ORDER_ADD, FlowTypeEnum.ORDER_CLOSED)),
    ORDER_COMPLAIN("ORDER_COMPLAIN", "客诉退款", Arrays.asList(FlowTypeEnum.ORDER_SUB)),
    TRANSFER_OUT("TRANSFER_OUT", "转账转出", Arrays.asList(FlowTypeEnum.DRIVER_SUB)),
    TRANSFER_IN("TRANSFER_IN", "他人转入",  Arrays.asList(FlowTypeEnum.DRIVER_ADD)),
    INVITE("INVITE", "邀请有奖",  Arrays.asList(FlowTypeEnum.INVITE_ORDER_REWARD, FlowTypeEnum.COMPENSATION_REFUND)),
    RESELL_ORDER("RESELL_ORDER", "订单转卖分佣",  Arrays.asList(FlowTypeEnum.RESELL_ORDER_FREEZE_ADD, FlowTypeEnum.RESELL_ORDER_ADD, FlowTypeEnum.RESELL_ORDER_CLOSED)),
    RESELL_ORDER_COMPLAIN("RESELL_ORDER_COMPLAIN", "订单转卖客诉退款", Arrays.asList(FlowTypeEnum.RESELL_ORDER_SUB)),
    ARTIFICIAL_ADJUSTMENT("ARTIFICIAL_ADJUSTMENT", "人工调账",  List.of( FlowTypeEnum.ARTIFICIAL_ADJUSTMENT));
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 流水类型
     */
    private final List<FlowTypeEnum> type;


    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static FlowSelectEnum getByCode(String code) {
        for (FlowSelectEnum itemEnum : FlowSelectEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (FlowSelectEnum itemEnum : FlowSelectEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    public static String getInfoByType(String type) {
        FlowTypeEnum flowType = FlowTypeEnum.getByCode(type);
        if (flowType != null) {
            for (FlowSelectEnum itemEnum : FlowSelectEnum.values()) {
                if (itemEnum.getType().contains(flowType)) {
                    return itemEnum.getInfo();
                }
            }
        }
        return null;
    }
}

