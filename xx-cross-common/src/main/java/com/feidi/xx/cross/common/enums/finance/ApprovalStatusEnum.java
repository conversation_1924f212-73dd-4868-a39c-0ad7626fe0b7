
package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 账单审批状态
 * 审批状态：0待审批，1已通过，2已驳回，3已撤销
 */
@Getter
@AllArgsConstructor
public enum ApprovalStatusEnum {
    PENDING(0, "待审批", "创建"),
    APPROVED(1, "已通过", "审批通过"),
    REJECTED(2, "已驳回", "审批驳回"),
    CANCELED(3, "已撤销", "撤销");

    private final Integer code;
    private final String info;

    private final String logInfo;

    public static ApprovalStatusEnum fromCode(Integer code) {
        for (ApprovalStatusEnum status : values()) {
            if (Objects.equals(status.code, code)) return status;
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}