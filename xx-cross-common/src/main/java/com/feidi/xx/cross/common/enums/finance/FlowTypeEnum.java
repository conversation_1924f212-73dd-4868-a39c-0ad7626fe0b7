package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 财务 转账类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FlowTypeEnum {

    ADMIN_ADD("1", "管理员调账加钱", "管理员调入", "N"),
    ADMIN_SUB("2", "管理员调账扣款", "管理员调出", "N"),

    ADMIN_TRANSFER_ADD("11", "管理员内部转进", "管理员转进", "N"),
    ADMIN_TRANSFER_SUB("12", "管理员内部转出", "管理员转出", "N"),

    ORDER_FREEZE_ADD("21", "订单返利-资金冻结", "入账中", "Y"),
    ORDER_ADD("22", "订单返利-资金解冻", "已入账", "Y"),
    ORDER_SUB("23", "订单客诉扣款", "客诉扣款", "Y"),
    ORDER_CLOSED("24", "订单客诉扣款", "已关闭", "Y"),
    ORDER_REBATE("25", "订单结算", null, "N"),


    DRIVER_ADD("31", "司机内部转进", "他人转入", "Y"),
    DRIVER_SUB("32", "司机内部内部转出", "转出", "Y"),

    CASHING("43", "发起提现", "发起提现", "Y"),
    CASH_SUB("41", "发起提现-成功", "提现成功", "Y"),
    CASH_FAIL("44", "发起提现-失败", "提现失败", "Y"),
    CASH_ADD("42", "提现失败回款", "提现失败", "Y"),

    //INVITE_ORDER_REWARD_FREEZE("51", "拉新完单奖励", "拉新完单奖励", "Y"),
    INVITE_ORDER_REWARD("51", "拉新完单奖励", "拉新完单奖励", "Y"),
    COMPENSATION_REFUND("52", "拉新客诉退款", "拉新客诉退款", "Y"),

    RESELL_ORDER_FREEZE_ADD("61", "订单转卖-资金冻结", "入账中", "Y"),
    RESELL_ORDER_ADD("62", "订单转卖-资金解冻", "已入账", "Y"),
    RESELL_ORDER_SUB("63", "订单转卖客诉扣款", "客诉扣款", "Y"),
    RESELL_ORDER_CLOSED("64", "订单转卖客诉扣款", "已关闭", "Y"),
    RESELL_ORDER_REBATE("65", "订单转卖结算", null, "N"),

    REWARD_ADD("91", "奖励", "奖励", "N"),

    ARTIFICIAL_ADJUSTMENT("101", "人工调账", "人工调账", "N"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 描述
     */
    private final String info;

    /**
     * 展示文本
     */
    private final String isShow;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static FlowTypeEnum getByCode(String code) {
        for (FlowTypeEnum itemEnum : FlowTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (FlowTypeEnum itemEnum : FlowTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    /**
     * 是否冻结操作
     *
     * @param typeEnum
     * @return
     */
    public static boolean isFreezeType(FlowTypeEnum typeEnum) {
        return Arrays.asList(ORDER_FREEZE_ADD, CASHING).contains(typeEnum);
    }
}

