package com.feidi.xx.cross.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 城市名字和线路名称设置工具类
 *
 * <AUTHOR>
 */
@Component
public class NameSetterUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        NameSetterUtil.applicationContext = applicationContext;
    }

    public static RemoteLineService getRemoteLineService() {
        return applicationContext.getBean(RemoteLineService.class);
    }

    /**
     * 获取城市名称映射
     *
     * @param cityCodes 城市编码列表
     * @return 城市编码到名称的映射
     */
    public static Map<String, String> getCityNameMap(Collection<String> cityCodes) {
        if (CollUtil.isEmpty(cityCodes)) {
            return Collections.emptyMap();
        }
        Map<String, SysDistrictCacheVo> cityInfo = RedisUtils.getMultiCacheMapValue(
                SystemCacheKeyEnum.SYS_CITY_CODE_KEY.create(), new HashSet<>(cityCodes));
        if (CollUtil.isEmpty(cityInfo)) {
            return Collections.emptyMap();
        }
        return cityInfo.values().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        SysDistrictCacheVo::getCityCode,
                        SysDistrictCacheVo::getName,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 获取线路名称映射
     *
     * @param lineIds 线路ID列表
     * @return 线路ID到名称的映射
     */
    public static Map<Long, String> getLineNameMap(Collection<Long> lineIds) {
        if (CollUtil.isEmpty(lineIds)) {
            return Collections.emptyMap();
        }
        List<RemoteLineVo> remoteLineVos = getRemoteLineService().queryByLineIds(new ArrayList<>(lineIds));
        if (CollUtil.isEmpty(remoteLineVos)) {
            return Collections.emptyMap();
        }

        return remoteLineVos.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        RemoteLineVo::getId,
                        RemoteLineVo::getName,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 批量设置城市名称
     *
     * @param collection 数据集合
     * @param getter     获取城市编码的函数
     * @param setter     设置城市名称的函数
     * @param <T>        数据类型
     */
    public static <T> void cityNameSetterByCode(Collection<T> collection, Function<T, List<String>> getter, BiConsumer<T, List<String>> setter) {
        if (CollUtil.isEmpty(collection)) {
            return;
        }

        Set<String> cityCodes = collection.stream()
                .map(getter)
                .filter(Objects::nonNull)
                .flatMap(List::stream)  // 这里改为 List::stream
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        if (cityCodes.isEmpty()) {
            return;
        }

        Map<String, String> code2Name = getCityNameMap(cityCodes);

        collection.forEach(item -> {
            List<String> cityCodeList = getter.apply(item);
            if (CollUtil.isNotEmpty(cityCodeList)) {
                List<String> cityNameList = cityCodeList.stream()
                        .filter(Objects::nonNull)
                        .map(code2Name::get)
                        .filter(StrUtil::isNotBlank)
                        .toList();
                setter.accept(item, cityNameList);
            }
        });
    }

    /**
     * 设置单个城市名称
     *
     * @param collection 数据集合
     * @param getter     获取城市编码的函数
     * @param setter     设置城市名称的函数
     * @param <T>        数据类型
     */
    public static <T> void cityNameSetterBySingleCode(Collection<T> collection, Function<T, String> getter, BiConsumer<T, String> setter) {
        if (CollUtil.isEmpty(collection)) {
            return;
        }

        Set<String> cityCodes = collection.stream()
                .map(getter)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        if (cityCodes.isEmpty()) {
            return;
        }

        Map<String, String> code2Name = getCityNameMap(cityCodes);

        collection.forEach(item -> {
            String cityCode = getter.apply(item);
            if (cityCode != null) {
                String cityName = code2Name.get(cityCode);
                setter.accept(item, cityName);
            }
        });
    }

    /**
     * 批量设置线路名称
     *
     * @param collection 数据集合
     * @param getter     获取线路ID的函数
     * @param setter     设置线路名称的函数
     * @param <T>        数据类型
     */
    public static <T> void lineNameSetter(Collection<T> collection, Function<T, List<Long>> getter, BiConsumer<T, List<String>> setter) {
        if (CollUtil.isEmpty(collection)) {
            return;
        }

        Set<Long> lineIds = collection.stream()
                .map(getter)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (lineIds.isEmpty()) {
            return;
        }

        Map<Long, String> lineNameMap = getLineNameMap(lineIds);

        collection.forEach(item -> {
            Collection<Long> lineIdList = getter.apply(item);
            if (CollUtil.isNotEmpty(lineIdList)) {
                List<String> lineNameList = lineIdList.stream()
                        .filter(Objects::nonNull)
                        .map(lineNameMap::get)
                        .filter(StrUtil::isNotBlank)
                        .toList();
                setter.accept(item, lineNameList);
            }
        });
    }

    /**
     * 设置单个线路名称
     *
     * @param collection 数据集合
     * @param getter     获取线路ID的函数
     * @param setter     设置线路名称的函数
     * @param <T>        数据类型
     */
    public static <T> void lineNameSetterBySingleId(Collection<T> collection, Function<T, Long> getter, BiConsumer<T, String> setter) {
        if (CollUtil.isEmpty(collection)) {
            return;
        }

        Set<Long> lineIds = collection.stream()
                .map(getter)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (lineIds.isEmpty()) {
            return;
        }

        Map<Long, String> lineNameMap = getLineNameMap(lineIds);

        collection.forEach(item -> {
            Long lineId = getter.apply(item);
            if (lineId != null) {
                String lineName = lineNameMap.get(lineId);
                setter.accept(item, lineName);
            }
        });
    }
}
