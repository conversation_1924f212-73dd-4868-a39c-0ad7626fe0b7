package com.feidi.xx.cross.common.enums.operate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 价格类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PricingStatusEnum {

    active("0", "生效中"),

    invalid("1", "未配置");

    @EnumValue
    private final String code;
    private final String info;


    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static PricingStatusEnum getByCode(String code) {
        for (PricingStatusEnum itemEnum : PricingStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (PricingStatusEnum itemEnum : PricingStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

}
