package com.feidi.xx.cross.common.enums.passenger;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 乘客收藏地址枚举
 *
 * <AUTHOR>
 * @date 2025/6/11
 */
@Getter
@AllArgsConstructor
public enum PassengerCollectAddressEnum {

    Home("1", "家"),

    Company("2", "公司"),

    Other("3", "其他");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;
}
