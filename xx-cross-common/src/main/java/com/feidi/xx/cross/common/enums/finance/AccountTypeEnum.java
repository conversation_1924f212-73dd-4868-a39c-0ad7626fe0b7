package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账户类型 枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AccountTypeEnum {

    /**
     * 这里的枚举类型必须跟 {@link PaymentTypeEnum}的保持一致
     * 这里的枚举类型必须跟 {@link PaymentTypeEnum}的保持一致
     * 这里的枚举类型必须跟 {@link PaymentTypeEnum}的保持一致
     * 这里的枚举类型必须跟 {@link PaymentTypeEnum}的保持一致
     * 这里的枚举类型必须跟 {@link PaymentTypeEnum}的保持一致
     */
    ALIPAY("1", "支付宝"),
    WECHAT("2", "微信"),
    BANK("3", "银行卡");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static AccountTypeEnum getByCode(String code) {
        for (AccountTypeEnum itemEnum : AccountTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (AccountTypeEnum itemEnum : AccountTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

