package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DrvCertStatusEnum {

    UPLOAD("0", "去上传"),
    MODIFY("1", "去修改"),
    ;

    /**
     *
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static DrvCertStatusEnum getByCode(String code) {
        for (DrvCertStatusEnum itemEnum : DrvCertStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DrvCertStatusEnum itemEnum : DrvCertStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}
