package com.feidi.xx.cross.common.constant.order;

/**
 * 订单锁相关常量信息
 *
 * <AUTHOR>
 * @date 2025-3-18
 */
public interface OrderLockKeyConstants {

    /**
     * 锁过期时间
     */
    int LOCK_EXPIRE_TIME = 30;

    /**
     * 相关缓存前缀
     */
    String CACHE_PREFIX = "ord:lock:";

    /**
     * 订单锁key
     */
    String LOCK_ORDER_KEY = CACHE_PREFIX + ":order:";

    /**
     * 订单完单锁key
     */
    String LOCK_ORDER_FINISH_KEY = CACHE_PREFIX + ":order:finish:";

    /**
     * 订单自动抢单锁key
     */
    String LOCK_ORDER_ROB_KEY = CACHE_PREFIX + ":order:rob:";

    /**
     * 订单调度锁key
     */
    String LOCK_ORDER_DISPATCH_KEY = CACHE_PREFIX + ":order:dispatch:";

}

