package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运力 驱动类型 枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DriveTypeEnum {

    // 0燃油 1纯电 2混动
    FUEL("0", "燃油"),
    ELECTRIC("1", "纯电"),
    HYBRID("2", "混动");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static DriveTypeEnum getByCode(String code) {
        for (DriveTypeEnum itemEnum : DriveTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DriveTypeEnum itemEnum : DriveTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

