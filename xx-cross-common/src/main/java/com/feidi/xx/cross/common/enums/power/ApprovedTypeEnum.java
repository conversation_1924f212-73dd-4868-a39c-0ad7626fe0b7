package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  准驾车型枚举
 */
@Getter
@AllArgsConstructor
public enum ApprovedTypeEnum {

    A1("1", "A1"),
    A2("2", "A2"),
    A3("3", "A3"),
    B1("4", "B1"),
    B2("5", "B2"),
    C1("6", "C1"),
    C2("7", "C2"),
    C3("8", "C3"),
    C4("9", "C4"),
    D("10", "D"),
    E("11", "E"),
    Taxi("12", "Taxi"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static ApprovedTypeEnum getByCode(String code) {
        for (ApprovedTypeEnum itemEnum : ApprovedTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (ApprovedTypeEnum itemEnum : ApprovedTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}
