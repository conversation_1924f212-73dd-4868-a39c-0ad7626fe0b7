package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运力 司机证件类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DrvCertTypeEnum {

    ID_FRONT("1", "身份证正面"),
    ID_BACK("2", "身份证反面"),
    LICENSE("3", "行驶证正页"),
    DRIVING("4", "驾驶证正页"),
    POLICY("5", "保单"),
    CAR("6", "车辆"),
    LICENSE_BACK("7","行驶证副页"),
    DRIVING_BACK("8","驾驶证副页"),
    AGREEMENT("9","自营-代扣协议"),
    HELD_ID("10", "自营-手持身份证"),
    HELD_AGREEMENT("11","自营-手持代扣协议"),
    AUTH("12", "人脸认证"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static DrvCertTypeEnum getByCode(String code) {
        for (DrvCertTypeEnum itemEnum : DrvCertTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DrvCertTypeEnum itemEnum : DrvCertTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

