package com.feidi.xx.cross.common.enums.platform;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 美团退款类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MtRefundTypeEnum {
    /**
     * 已撤销
     */
    FULL("FULL", "全额退"),
    /**
     * 草稿
     */
    PART("PART", "部分退"),
    ;

    /**
     * 状态
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;
}

