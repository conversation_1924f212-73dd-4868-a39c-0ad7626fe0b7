package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 优惠卷状态
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum CouponStatusEnum {

    NOT_USED("0", "待使用"),
    USED("1", "已使用"),
    EXPIRED("2", "已过期"),
    INVALID("3", "已作废");

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (CouponStatusEnum itemEnum : CouponStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

