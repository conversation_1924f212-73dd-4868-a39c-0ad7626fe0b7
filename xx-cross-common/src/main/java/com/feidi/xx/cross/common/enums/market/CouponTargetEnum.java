package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 优惠卷状态
 *
 * @see GrantScopeEnum
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum CouponTargetEnum {

    LINE("0", "线路专属"),
    PLATFORM("1", "全平台通用"),
    CITY("2", "城市专属"),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (CouponTargetEnum itemEnum : CouponTargetEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

