package com.feidi.xx.cross.common.enums.operate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 价格类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PriceTypeEnum {

    STEP("step", "阶梯计价"),
    FIXED("fixed", "一口价");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static PriceTypeEnum getByCode(String code) {
        for (PriceTypeEnum itemEnum : PriceTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (PriceTypeEnum itemEnum : PriceTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

