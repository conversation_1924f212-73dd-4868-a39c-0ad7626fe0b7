package com.feidi.xx.cross.common.constant.order;

import com.feidi.xx.common.core.constant.GlobalConstants;

/**
 * 订单服务常用给的常量信息
 *
 * <AUTHOR>
 */
public interface OrderConstants {

    /**
     *  默认缓存时间
     */
    Integer DEFAULT_TIME = 20;

    /**
     * 支付超时时间 单位：分
     */
    Integer PAYMENT_TIMEOUT_TIME = 30;

    /**
     * 支付状态变更时间 单位：分
     */
    Integer PAYMENT_TYPE_TIMEOUT_TIME = 2;

    /**
     * 接单超时时间 单位：分
     */
    Integer RECEIVE_TIMEOUT_TIME = 10;

    /**
     * 订单支付状态同步流水时间 一天
     */
    Integer PAYMENT_SYNC_TIME = 60 * 24;

    /**
     * 相关缓存前缀
     */
    String CACHE_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + "cx:";

    /**
     * 订单返利周期 默认时间（7天）
     */
    String CX_REBATE_PERIOD_DEFAULT = "8";

    /**
     * 城际顺风车订单返利周期配置KEY
     */
    String CX_REBATE_PERIOD = "cross.rebate.period";

    /**
     * 自营平台订单返利周期 默认时间（7天）
     */
    String CX_REBATE_SELF_PERIOD_DEFAULT = "8";

    /**
     * 自营平台订单返利周期配置KEY
     */
    String CX_REBATE_SELF_PERIOD = "cross.rebate.self.period";

    /**
     * 第三方平台订单返利周期 默认时间（1天）
     */
    String CX_REBATE_PLATFORM_PERIOD_DEFAULT = "2";

    /**
     * 第三方平台订单返利周期配置KEY
     */
    String CX_REBATE_PLATFORM_PERIOD = "cross.rebate.platform.period";

    /**
     * 订单返利 缓存队列的KEY
     */
    String CX_REBATE_CACHE_KEY = CACHE_PREFIX + "LIST:REBATE:";

    /**
     * 订单位置 缓存队列的KEY
     */
    String ORDER_POSITION = CACHE_PREFIX + "position:";

    /**
     *  配置 KEY 相关
     */

    /**
     *  司机抢单最大数量
     */
    String ROB_MAX = "cx.rob.max";

    /**
     * 司机常用路线数量
     */
    String ROB_FAVORITE_ROUTE = "frequent_route_count";

    /**
     * 订单返利
     */
    String REBATE_ORDER = CACHE_PREFIX + "rebate";

    /**
     * 订单位置缓存时间
     */
    Integer ORDER_POSITION_TIME = 6 * 60;

    /**
     * 订单验证手机尾号
     */
    String ORDER_CHECK_PHONE_END = CACHE_PREFIX + "check:phone:end";

    /**
     * 订单验证手机尾号缓存时间
     */
    Integer ORDER_CHECK_PHONE_END_TIME = 60;

    /**
     * 订单验证手机尾号最大错误次数
     */
    Integer ORDER_CHECK_ERROR_MAX_NUMBER = 5;

    /**
     * 订单验证手机尾号
     */
    String ORDER_MT_PASSENGER_PHONE = CACHE_PREFIX + "passenger:phone:";

    /**
     * 订单验证手机尾号缓存时间
     */
    Integer ORDER_MT_PHONE_TIME = 30;

    /**
     * 紧急联系电话
     */
    String ORDER_EMERGENCY_PHONE = "cx.emergency.phone";

    /**
     * 订单结算ids 缓存队列的KEY
     */
    String ORDER_REBATE_IDS = CACHE_PREFIX + "rebate:ids:";

    /**
     * 订单结算ids 缓存时间（小时）
     */
    Integer ORDER_REBATE_IDS_TIME = 24;

    /**
     * 订单过滤默认时间(分钟)
     */
    Integer ORDER_FILTER_TIM = 30;

    /**
     * 订单过滤缓存KEY
     */
    String ORDER_FILTER_KEY = GlobalConstants.GLOBAL_REDIS_KEY + "order:filter:";

    /**
     * 订单默认可见时间 单位：秒
     */
    String ORDER_SCOPE_TIME_DEFAULT = "60";

    /**
     * 拉新订单司机可见时间 单位：秒
     * 取不到值默认ORDER_SCOPE_TIME_DEFAULT
     */
    String ORDER_SCOPE_TIME_DRV_KET = "cross_scope_time_drv";

    /**
     * 拉新订单代理商可见时间 单位：秒
     * 取不到值默认ORDER_SCOPE_TIME_DEFAULT
     */
    String ORDER_SCOPE_TIME_AGT_KET = "cross_scope_time_agt";

    /**
     * 城际顺风车删除取消订单数据周期配置KEY
     */
    String CX_DELETE_CANCEL_PERIOD = "cross.delete.cancel.period";

    /**
     * 删除取消订单数据周期 默认时间（30天）
     */
    String CX_DELETE_CANCEL_PERIOD_DEFAULT = "30";

    /**
     * 订单转卖乘客端最小支付金额配置KEY
     */
    String CROSS_RESELL_PASSENGER_MIN_PAY = "cross.resell.passenger.min.pay";

    /**
     * 订单转卖司机接单金额比例配置KEY
     */
    String CROSS_RESELL_DRIVER_PRICE_RATE = "cross.resell.driver.price.rate";
    /**
     *后台订单导入愿等时间
     */
    String WAIT_TIMEOUT_ORDER_RECORDING = "waiting.time.order.recording";




}

