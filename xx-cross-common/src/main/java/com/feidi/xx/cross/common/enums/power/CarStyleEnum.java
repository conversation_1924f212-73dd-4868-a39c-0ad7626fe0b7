package com.feidi.xx.cross.common.enums.power;

import com.feidi.xx.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运力 车辆样式 枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CarStyleEnum {

    // 0轿车 1SUV 2MPV
    CAR("1", "轿车"),
    SUV("2", "SUV"),
    MPV("3", "MPV");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static CarStyleEnum getByCode(String code) {
        for (CarStyleEnum itemEnum : CarStyleEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (CarStyleEnum itemEnum : CarStyleEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    /**
     * 根据INFO获取CODE
     * @param info
     * @return
     */
    public static String getCodeByInfo(String info) {
        for (CarStyleEnum itemEnum : CarStyleEnum.values()) {
            if (StringUtils.isNotBlank(info) && info.contains(itemEnum.getCode())) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

