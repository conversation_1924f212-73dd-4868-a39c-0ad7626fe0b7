package com.feidi.xx.cross.common.enums.platform;

import com.feidi.xx.common.core.enums.SexEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 性别
 *
 * <AUTHOR>
 * @date 2024/9/10
 */
@Getter
@AllArgsConstructor
public enum MtSexEnum {
    MAN(1, "男", SexEnum.MAN.getCode()),
    WOMAN(2, "女", SexEnum.WOMAN.getCode());

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String info;

    private final String sex;

    /**
     * 根据性别获取code
     * @param sex
     * @return
     */
    public static Integer getCodeBySex(String sex) {
        for (MtSexEnum value : MtSexEnum.values()) {
            if (value.getSex().equals(sex)) {
                return value.getCode();
            }
        }
        return null;
    }
}

