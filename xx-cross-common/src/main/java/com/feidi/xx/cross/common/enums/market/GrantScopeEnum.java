package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 发放范围
 *
 * @see CouponTargetEnum
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum GrantScopeEnum {

    ALL("0", "线路"),
    CITY("1", "通用"),
    LINE("2", "城市"),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (GrantScopeEnum itemEnum : GrantScopeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(Arrays.stream(values()).map(e -> e.getCode() + ":" + e.getInfo()).collect(Collectors.joining(";")));
    }
}

