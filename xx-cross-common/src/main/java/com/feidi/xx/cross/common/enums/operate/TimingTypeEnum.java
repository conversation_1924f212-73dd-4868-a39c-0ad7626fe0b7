package com.feidi.xx.cross.common.enums.operate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 时效 枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TimingTypeEnum {

    REAL("0", "实时", "Y"),
    SUBSCRIBE("1", "预约","Y"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 是否显示 Y-显示 N-不显示
     */
    private final String isShow;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static TimingTypeEnum getByCode(String code) {
        for (TimingTypeEnum itemEnum : TimingTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (TimingTypeEnum itemEnum : TimingTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

}

