package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 运力 司机类型 枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DriverTypeEnum {
    PARTNER("0","代理商加盟","Y", DrvGroupTypeEnum.JOIN.getCode()),
    SELF("1","代理商自营","Y", DrvGroupTypeEnum.SELF.getCode()),
    HIGH_SEAS("2","公海","N", null),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 是否展示
     */
    private final String isShow;

    /**
     * 司机组类型
     */
    private final String groupType;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static DriverTypeEnum getByCode(String code) {
        for (DriverTypeEnum itemEnum : DriverTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DriverTypeEnum itemEnum : DriverTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    /**
     * 根据司机组类型获取司机类型
     *
     * @param groupType 司机组类型
     * @return 司机类型
     */
    public static String getCodeByGroupType(String groupType) {
        for (DriverTypeEnum itemEnum : DriverTypeEnum.values()) {
            if (Objects.equals(groupType, itemEnum.getGroupType())) {
                return itemEnum.getCode();
            }
        }
        return null;
    }
}
