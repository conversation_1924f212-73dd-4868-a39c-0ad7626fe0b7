package com.feidi.xx.cross.common.enums.operate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 线路类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LineTypeEnum {
    /**
     * 行政区
     */
    REGION("1", "行政区"),
    /**
     * 电子围栏
     */
    RAIL("2", "电子围栏");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static LineTypeEnum getByCode(String code) {
        for (LineTypeEnum itemEnum : LineTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (LineTypeEnum itemEnum : LineTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

