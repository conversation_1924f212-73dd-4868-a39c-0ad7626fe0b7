package com.feidi.xx.cross.common.enums.platform;

import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 美团订单状态枚举
 *
 * <AUTHOR>
 * @date 2024/9/9
 */
@Getter
@AllArgsConstructor
public enum MtOrderStatusEnum {

    SUBMIT("SUBMIT", 10, "提交订单", OrderStatusEnum.CREATE.getCode()),
    CONFIRM("CONFIRM", 30, "已接单（上报运力）", OrderStatusEnum.RECEIVE.getCode()),
    REASSIGN("REASSIGN", 40, "改派", null),
    SET_OUT("SET_OUT", 50, "司机已出发", OrderStatusEnum.PICK.getCode()),
    ARRIVE("ARRIVE", 60, "司机已到达", OrderStatusEnum.PICK_START.getCode()),
    DRIVING("DRIVING", 70, "行驶中(乘客确认上车)", OrderStatusEnum.ING.getCode()),
    DELIVERED("DELIVERED", 80, "已到达(乘客确认下车)", OrderStatusEnum.FINISH.getCode()),
    CANCEL_BY_USER("CANCEL_BY_USER", 101, "乘客撤单", null),
    CANCEL_BY_DRIVER("CANCEL_BY_DRIVER", 102, "司机撤单", null),
    CANCEL_BY_CS("CANCEL_BY_CS", 103, "客服撤单", OrderStatusEnum.CANCEL.getCode()),
    ;

    private final String code;
    private final int encoding;
    private final String info;
    private final String orderStatus;

    /**
     * 根据订单状态获取code
     *
     * @param orderStatus 订单状态
     * @return code
     */
    public static String getCodeByOrderStatus(String orderStatus) {
        for (MtOrderStatusEnum itemEnum : MtOrderStatusEnum.values()) {
            if (Objects.equals(itemEnum.getOrderStatus(), orderStatus)) {
                return itemEnum.getCode();
            }
        }
        return null;
    }
}
