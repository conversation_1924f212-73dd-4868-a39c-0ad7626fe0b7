package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  司机同意场景枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DriverConsentSceneEnum {
    LOGIN("0", "登录"),
    REGISTER("1", "注册"),
    WITHDRAW("2", "提现"),
    BIND("3", "绑定提现账户"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static DriverConsentSceneEnum getByCode(String code) {
        for (DriverConsentSceneEnum itemEnum : DriverConsentSceneEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DriverConsentSceneEnum itemEnum : DriverConsentSceneEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}
