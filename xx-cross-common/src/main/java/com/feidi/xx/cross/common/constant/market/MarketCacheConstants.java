package com.feidi.xx.cross.common.constant.market;

import com.feidi.xx.common.core.constant.GlobalConstants;

/**
 * 运营服务 缓存常量
 *
 * <AUTHOR>
 */
public interface MarketCacheConstants {

    /**
     * 运营服务相关缓存前缀
     */
    String CACHE_PREFIX = "mkt:";

    /**
     * 平台信息
     */
    String PLATFORM_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "platform:";

    /**
     * 产品信息
     */
    String PRODUCT_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "product:";

    /**
     * 价格信息
     */
    String PRICING_INFO_PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + CACHE_PREFIX + "pricing:";


    /**
     * 邀请有奖是否开启key
     */
    String INVITE_REWARD = "invite_reward";



    /**
     * 邀请有奖司机时间key
     */
    String INVITE_DRIVER_TIME = "order_invite_reward";

    /**
     * 拉新返奖默认比例
     */
    String RECRUIT_REWARD_RATIO = "mkt.recruit.reward.default.ratio";

}
