package com.feidi.xx.cross.common.enums.passenger;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  司机同意场景枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PassengerConsentSceneEnum {
    LOGIN("0", "登录"),
    REGISTER("1", "注册"),
    NOTICE("2", "预约服务告知"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static PassengerConsentSceneEnum getByCode(String code) {
        for (PassengerConsentSceneEnum itemEnum : PassengerConsentSceneEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (PassengerConsentSceneEnum itemEnum : PassengerConsentSceneEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}
