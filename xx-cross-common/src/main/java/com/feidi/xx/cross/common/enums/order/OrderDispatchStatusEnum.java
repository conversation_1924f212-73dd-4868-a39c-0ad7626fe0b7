package com.feidi.xx.cross.common.enums.order;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 调度端-订单状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderDispatchStatusEnum {
    ///  null.全部   2 未出行   5 行程中   0.取消   6完成   10.未出行&行程中
    ALL("0", "全部", null, "Y"),
    NOT_DISPATCH("10", "待派", Collections.singletonList(OrderStatusEnum.CREATE.getCode()), "Y"),
    DISPATCH("20", "已派", Arrays.asList(OrderStatusEnum.RECEIVE.getCode(), OrderStatusEnum.PICK.getCode(), OrderStatusEnum.PICK_START.getCode(), OrderStatusEnum.ING.getCode()), "Y"),
    FINISH("30", "完单", Collections.singletonList(OrderStatusEnum.FINISH.getCode()), "Y"),
    RECEIVE("40", "已接单", Collections.singletonList(OrderStatusEnum.RECEIVE.getCode()), "Y"),
    ING("50", "行程中", Arrays.asList(OrderStatusEnum.PICK.getCode(), OrderStatusEnum.PICK_START.getCode(), OrderStatusEnum.ING.getCode()), "Y"),
    //司机订单全部 已接单-行程中
    DRIVER_ALL("60", "全部", Arrays.asList(OrderStatusEnum.RECEIVE.getCode(),OrderStatusEnum.PICK.getCode(), OrderStatusEnum.PICK_START.getCode(), OrderStatusEnum.ING.getCode()), "Y"),

    COMPLAIN("100", "客诉", null, "Y"),
    ;

    private final String code;
    private final String info;
    private final List<String> statusList;
    private final String isShow;

    public static String getInfoByCode(String code){
        if(StrUtil.isEmpty(code)){
            return null;
        }
        for (OrderDispatchStatusEnum value : OrderDispatchStatusEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value.getInfo();
            }
        }
        return null;
    }

    public static List<String> getStatusListByCode(String code){
        if(StrUtil.isEmpty(code)){
            return null;
        }
        for (OrderDispatchStatusEnum value : OrderDispatchStatusEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value.getStatusList();
            }
        }
        return null;
    }

}
