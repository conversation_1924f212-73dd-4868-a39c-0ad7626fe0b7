package com.feidi.xx.cross.common.enums.market;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 角色类型(1邀请人,2被邀请人)
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PassengerInviteRoleTypeEnum {
    INVITER("1", "邀请人"),
    INVITEE("2", "被邀请人"),
    ;
    private final String code;

    private final String info;

    /**
     * 获取枚举值
     *
     * @param code 枚举值
     * @return 枚举值
     */
    public static PassengerInviteRoleTypeEnum getByCode(String code) {
        for (PassengerInviteRoleTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
