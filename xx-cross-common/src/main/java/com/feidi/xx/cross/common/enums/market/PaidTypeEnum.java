package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 领取方式[自动发放|人工发放|购买]
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum PaidTypeEnum {

    AUTO("1", "自动"),
    ARTIFICIAL("2", "手动"),
    BUY("3", "购买"),
    GET("4", "领取"),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (PaidTypeEnum itemEnum : PaidTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

