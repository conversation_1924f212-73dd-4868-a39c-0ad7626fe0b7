package com.feidi.xx.cross.common.enums.operate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 电子围栏类型
 */
@Getter
@AllArgsConstructor
public enum FenceTypeEnum {

    AIRPORT("1", "机场围栏"),
    TRAIN_STATION("2","火车站围栏"),
    BUS_STATION("3","汽车站围栏"),
    CITY("4","城区围栏"),
    SCENIC_AREA("5","景区围栏"),
    BUSINESS_DISTRICT("6", "商圈围栏"),

    DIY("49", "自定义围栏"),
    OTHER("99", "其他")
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static FenceTypeEnum getByCode(String code) {
        for (FenceTypeEnum itemEnum : FenceTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (FenceTypeEnum itemEnum : FenceTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}
