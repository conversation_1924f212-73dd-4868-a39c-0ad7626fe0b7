package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 对账明细类型 枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FinanceTypeEnum {

    PROFIT("0", "佣金"),
    REWARD("1", "佣金奖励"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    public static String getInfoByCode(String code) {
        for (FinanceTypeEnum item : FinanceTypeEnum.values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item.getInfo();
            }
        }
        return null;
    }

}

