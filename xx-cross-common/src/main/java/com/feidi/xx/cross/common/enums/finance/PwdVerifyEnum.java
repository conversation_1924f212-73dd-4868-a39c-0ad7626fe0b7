package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 财务 体现审核状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PwdVerifyEnum {
    RESET("1", "重置交易密码"),
    CASH("2", "提现"),
    TRANSFER("3", "转账"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static PwdVerifyEnum getByCode(String code) {
        for (PwdVerifyEnum itemEnum : PwdVerifyEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (PwdVerifyEnum itemEnum : PwdVerifyEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}
