package com.feidi.xx.cross.common.enums.market;

import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品范围
 *
 * <AUTHOR>
 * @see ProductCodeEnum
 */
@Getter
@AllArgsConstructor
public enum ProductScopeEnum {

    ALL("0", "全部", null),
    EXCLUSIVE("1", "独享", ProductCodeEnum.RENT),
    CARPOOL("2", "拼车", ProductCodeEnum.FIT);

    private final String code;
    private final String info;

    private final ProductCodeEnum productCode;


    public static ProductScopeEnum fromCode(String code) {
        for (ProductScopeEnum scope : values()) {
            if (scope.getCode().equals(code)) {
                return scope;
            }
        }
        return null;
    }

    public static ProductScopeEnum fromProductCode(String productCode) {
        for (ProductScopeEnum scope : values()) {
            if (scope.getProductCode() == null) {
                continue;
            }
            if (scope.getProductCode().getCode().equals(productCode)) {
                return scope;
            }
        }
        return null;
    }
}