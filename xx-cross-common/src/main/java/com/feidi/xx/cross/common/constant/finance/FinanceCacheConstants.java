package com.feidi.xx.cross.common.constant.finance;

/**
 * 财务管理静态常量
 */
public interface FinanceCacheConstants {

    /**
     * 等待获取锁时间，单位秒
     */
    int LOCK_EXPIRE_TIME = 30;

    /**
     * 自动付款
     */
    String LOCK_AUTO_PAY_KEY = "lock:autoPay:";

    /**
     * 订单完单
     */
    String LOCK_ORDER_FIN_KEY = "lock:order:fin:";

    /**
     * 提现申请次数
     */
    String FX_CASH_APPLY_KEY = "fx:cash:apply";

    /**
     * 转账申请次数
     */
    String FX_TRANSFER_APPLY_KEY = "fx:transfer:apply";

    /**
     * 资产密码错误次数
     */
    String FX_PASSWORD_WRONG_TIMES_KEY = "fx:password:wrong:times";

}
