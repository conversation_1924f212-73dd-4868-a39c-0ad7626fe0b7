package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 投放渠道[微信公众号|抖音]
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum ActivityIssuingChannelEnum {

    WX_MINI_APP("1", "微信小程序"),
    TIKTOK("2", "抖音"),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (ActivityIssuingChannelEnum itemEnum : ActivityIssuingChannelEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

