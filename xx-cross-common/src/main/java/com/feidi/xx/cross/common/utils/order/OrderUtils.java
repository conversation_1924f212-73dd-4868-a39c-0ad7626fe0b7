package com.feidi.xx.cross.common.utils.order;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.xx.RandomUtils;

import java.util.Date;

/**
 * 订单工具类
 *
 * <AUTHOR>
 * @date 2025/3/15
 */
public class OrderUtils {

    /**
     * 生成订单预估价号
     *
     * @return 预估价号
     */
    public static String makeCode() {
        // 订单号 = 当前时间（毫秒级，15位） + 6位随机数 ，共21位
        return RandomUtils.randomString(4);
    }

    /**
     * 生成订单预估价号
     *
     * @return 预估价号
     */
    public static String makeEstimateKey() {
        // 订单号 = 当前时间（毫秒级，15位） + 6位随机数 ，共21位
        return DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSSSSS)
                + RandomUtils.randomInt(100000, 999999);
    }

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    public static String makeOrderNo() {
        // 订单号 = 当前时间（毫秒级，15位） + 6位随机数 ，共21位
        return DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSSSSS)
                + RandomUtils.randomInt(100000, 999999);
    }

    public static String makeOrderKey(String platformCode, String productCode, String passengerId, Date earliestTime, Date latestTime, String startLonLat, String endLonLat) {
        String keyStr = StrUtil.join("#", platformCode, productCode, passengerId, earliestTime, latestTime, startLonLat, endLonLat);
        return SecureUtil.md5(keyStr).toUpperCase();
    }

    public static String getPassengerName(String phone) {
        if (StrUtil.isBlank(phone)) {
            return "乘客";
        }
        return StrUtil.format("尾号**{}乘客", phone.substring(phone.length() - 2));
    }

    /**
     * 生成自动抢单号
     *
     * @return 自动抢单号
     */
    public static String makeRobNo() {
        // 订单号 = 当前时间（毫秒级，15位） + 6位随机数 ，共21位
        return "rob" + DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSSSSS) + RandomUtils.randomInt(1000, 9999);
    }
}
