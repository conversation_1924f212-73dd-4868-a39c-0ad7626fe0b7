package com.feidi.xx.cross.common.constant.im;

import cn.hutool.core.date.DateUtil;

import java.util.Date;

public interface ImConstants {

    /**
     * 快捷回复消息
     */
    String IM_DRIVER_QUICK_MESSAGE = "im_driver_quick_message";

    /**
     * 完单后IM对话最长保留时间，哈啰那边随时变动，需要动态配置，单位：小时
     */
    String IM_REVERSE_MAX_TIME = "im.reverse.max.time";

    /**
     * 会话最长查询时间，近三个月
     */
    Date IM_CONVERSATION_MAX_QUERY_TIME = DateUtil.offsetMonth(new Date(), -3);


}
