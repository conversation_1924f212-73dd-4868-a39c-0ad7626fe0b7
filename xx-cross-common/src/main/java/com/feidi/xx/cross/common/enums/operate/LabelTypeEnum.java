package com.feidi.xx.cross.common.enums.operate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 标签类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LabelTypeEnum {

    ORDER("1", "订单");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static LabelTypeEnum getByCode(String code) {
        for (LabelTypeEnum itemEnum : LabelTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (LabelTypeEnum itemEnum : LabelTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

