package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 客诉费用类型 枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ComplainFeeTypeEnum {

    DEDUCT("0", "多结算扣款"),
    ADJUST("1", "差异调整"),
    REWARD("2", "免抽佣奖励"),
    COMPLAIN("3", "客诉"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    public static String getInfoByCode(String code) {
        for (ComplainFeeTypeEnum item : ComplainFeeTypeEnum.values()) {
            if (Objects.equals(item.getCode(), code)) {
                return item.getInfo();
            }
        }
        return null;
    }

}

