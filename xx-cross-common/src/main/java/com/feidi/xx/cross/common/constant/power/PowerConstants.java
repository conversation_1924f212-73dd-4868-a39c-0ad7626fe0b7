package com.feidi.xx.cross.common.constant.power;

/**
 * 运力服务常用给的常量信息
 *
 * <AUTHOR>
 */
public interface PowerConstants {

    /**
     * 运力服务相关缓存前缀
     */
    String CACHE_PREFIX = "px:";

    /**
     * 司机信息
     */
    String DRIVER_INFO = CACHE_PREFIX + "driver:";

    /**
     * 司机组信息
     */
    String GROUP_INFO = CACHE_PREFIX + "group:";

    /**
     * 司机车辆信息
     */
    String DRIVER_CAR_INFO = CACHE_PREFIX + "car:";

    /**
     * 线路信息
     */
    String LINE_INFO = CACHE_PREFIX + "line:";

    /**
     * 代理信息
     */
    String AGENT_INFO = CACHE_PREFIX + "agent:";

    String CHILD_AGENT_UNUSED_MSG = "二级代理商不支持此功能";

    /**
     * 司机邀请链接
     */
    String DRIVER_INVITE_URL = CACHE_PREFIX + "driver:invite:url";

    /**
     * 代理商邀请链接
     */
    String AGENT_INVITE_URL = CACHE_PREFIX + "agent:invite:url";



    /**
     * 邀请有奖链接
     */
    String INVITE_PRIZE_REFERRAL_URL ="invite_prize_referral_url";
}

