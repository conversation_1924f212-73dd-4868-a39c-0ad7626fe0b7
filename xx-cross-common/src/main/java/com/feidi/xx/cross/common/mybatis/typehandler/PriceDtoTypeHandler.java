package com.feidi.xx.cross.common.mybatis.typehandler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;

import java.util.ArrayList;
import java.util.List;

public class PriceDtoTypeHandler extends AbstractJsonTypeHandler<List<PriceDto>> {

    public PriceDtoTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    public List<PriceDto> parse(String json) {
        List<PriceDto> result = new ArrayList<>();
        if (StrUtil.isNotBlank(json)) {
            JSONArray array = JSONUtil.parseArray(json);
            for (Object object : array) {
                result.add(JSONUtil.toBean((JSONObject) object, PriceDto.class));
            }
        }
        return result;
    }

    @Override
    public String toJson(List<PriceDto> obj) {
        if (obj == null) return null;
        return JSONUtil.toJsonStr(obj);
    }

}
