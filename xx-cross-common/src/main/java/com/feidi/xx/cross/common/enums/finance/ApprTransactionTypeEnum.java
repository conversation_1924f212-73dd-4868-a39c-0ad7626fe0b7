package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交易类型，1:人工调账
 */
@AllArgsConstructor
@Getter
public enum ApprTransactionTypeEnum {
    // 交易类型，1:人工调账
    ARTIFICIAL_ADJUSTMENT(1, "人工调账"),
    ;
    private final Integer code;
    private final String info;

    public ApprTransactionTypeEnum getByInfo(String info) {
        for (ApprTransactionTypeEnum value : ApprTransactionTypeEnum.values()) {
            if (value.getInfo().equals(info)) {
                return value;
            }
        }
        throw new IllegalArgumentException("No enum constant " + ApprTransactionTypeEnum.class.getCanonicalName() + "." + info);
    }

    public static ApprTransactionTypeEnum getInfoByCode(Integer code) {
        for (ApprTransactionTypeEnum value : ApprTransactionTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
