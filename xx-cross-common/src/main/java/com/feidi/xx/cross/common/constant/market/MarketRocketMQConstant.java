
package com.feidi.xx.cross.common.constant.market;

/**
 * 上报引擎层服务 RocketMQ 常量类
 * <p>
 * 作者：zyt
 * 开发时间：2024-07-14
 *
 * <AUTHOR>
 */
public final class MarketRocketMQConstant {

    /**
     * 订单邀请有奖延迟队列 Topic Key
     */
    public static final String XX_MARKET_INVITE_EXPIRED_COUPON_TOPIC = "xx_market_invite_expired_coupon_topic${unique-name:}";

    /**
     * 订单邀请有奖延迟队列 GC Key
     */
    public static final String XX_MARKET_INVITE_EXPIRED_COUPON_CG = "xx_market_invite_expired_coupon_cg${unique-name:}";

    /**
     * 乘客注册成功消费者组
     */
    public static final String XX_MARKET_PASSENGER_REGISTER_CONSUMER_GROUP = "xx-market-passenger-register-consumer-group${unique-name:}";

    /**
     * 订单状态变更消费者组
     */
    public static final String XX_MARKET_ORDER_STATUS_CHANGE_CONSUMER_GROUP = "xx-market-order-status-change-consumer-group${unique-name:}";
}
