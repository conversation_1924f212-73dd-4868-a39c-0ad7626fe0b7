package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 财务 转账类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TransferTypeEnum {

    // 资金转账类型
    REPLACE_CASH("1", "代提现");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static TransferTypeEnum getByCode(String code) {
        for (TransferTypeEnum itemEnum : TransferTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (TransferTypeEnum itemEnum : TransferTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

