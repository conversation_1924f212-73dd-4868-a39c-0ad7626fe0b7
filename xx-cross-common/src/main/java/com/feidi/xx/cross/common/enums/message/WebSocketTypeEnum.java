package com.feidi.xx.cross.common.enums.message;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 消息类型
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum WebSocketTypeEnum {
    ORDER("order", "订单"),
    CHAT("chat", "IM"),
    ANNOUNCEMENT("announcement", "公告"),
    DEVICE("device", "设备"),
    HEARTBEAT("heartbeat", "心跳"),
    COUPON("coupon", "优惠券"),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (WebSocketTypeEnum itemEnum : WebSocketTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

