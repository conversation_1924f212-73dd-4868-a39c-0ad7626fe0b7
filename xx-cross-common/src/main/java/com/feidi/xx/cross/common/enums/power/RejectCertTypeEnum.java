package com.feidi.xx.cross.common.enums.power;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 市内 后台审核- 驳回证件类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RejectCertTypeEnum {

    ID_CARD("1", "身份证"),

    DRIVING("2", "驾驶证"),

    CAR("3", "车辆信息"),

    AGREEMENT("4", "代扣协议信息"),
    ;

    ;
    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static RejectCertTypeEnum getByCode(String code) {
        for (RejectCertTypeEnum itemEnum : RejectCertTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (RejectCertTypeEnum itemEnum : RejectCertTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}
