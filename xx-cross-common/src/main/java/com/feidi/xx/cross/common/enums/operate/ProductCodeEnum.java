package com.feidi.xx.cross.common.enums.operate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ProductCodeEnum {
    RENT("rent", "独享"),
    FIT("fit", "拼车"),
    COSY("cosy", "舒适拼");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static ProductCodeEnum getByCode(String code) {
        for (ProductCodeEnum itemEnum : ProductCodeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (ProductCodeEnum itemEnum : ProductCodeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    /**
     * 根据INFO获取CODE
     * @param info
     * @return
     */
    public static String getCodeByInfo(String info) {
        for (ProductCodeEnum itemEnum : ProductCodeEnum.values()) {
            if (itemEnum.getInfo().equals(info)) {
                return itemEnum.getCode();
            }
        }
        return null;
    }
}

