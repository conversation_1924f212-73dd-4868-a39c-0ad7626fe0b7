package com.feidi.xx.cross.common.enums.operate;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计算类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CalculateTypeEnum {

    EVALUATE("0", "估价"),
    PLACE_ORDER("1", "下单"),
    CLOSE_ACCOUNT("2", "结算");


    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static CalculateTypeEnum getByCode(String code) {
        for (CalculateTypeEnum itemEnum : CalculateTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (CalculateTypeEnum itemEnum : CalculateTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

}

