package com.feidi.xx.cross.common.enums.message;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单消息类型枚举
 *
 * <AUTHOR>
 * @date 2025/03/29
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum OrderMessageTypeEnum {

    INVITE("10", "邀请有奖"),

    ORDER_STATUS("20", "订单状态更新"),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (OrderMessageTypeEnum itemEnum : OrderMessageTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

