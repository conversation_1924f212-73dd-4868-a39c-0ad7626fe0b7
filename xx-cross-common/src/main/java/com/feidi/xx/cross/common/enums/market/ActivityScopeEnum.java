package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 范围[全部|新|老]
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum ActivityScopeEnum {

    ALL("1", "全部"),
    NEW("2", "新用户"),
    OLD("3", "老用户");
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (ActivityScopeEnum itemEnum : ActivityScopeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(Arrays.stream(values()).map(itemEnum -> itemEnum.getCode() + ":" + itemEnum.getInfo()).collect(Collectors.joining(";")));
    }
}

