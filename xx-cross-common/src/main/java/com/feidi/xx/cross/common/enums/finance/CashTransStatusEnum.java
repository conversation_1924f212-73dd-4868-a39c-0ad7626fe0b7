package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CashTransStatusEnum {

    TRADE_SUCCESS("1", "打款成功"),
    TRADE_FAIL("2", "打款失败"),
    TRADING("3", "打款中"),

    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static CashTransStatusEnum getByCode(String code) {
        for (CashTransStatusEnum itemEnum : CashTransStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (CashTransStatusEnum itemEnum : CashTransStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

