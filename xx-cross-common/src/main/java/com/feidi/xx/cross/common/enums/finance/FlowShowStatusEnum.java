package com.feidi.xx.cross.common.enums.finance;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum FlowShowStatusEnum {
    PROCESSING("PROCESSING", "处理中"),
    FINISHED("FINISHED", "已完成"),
    CLOSED("CLOSED", "已关闭"),
    FAILED("FAILED", "已失败"),
    REWARD("REWARD", "已发放"),
    FINED("FINED", "已扣款");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     *
     * @param code
     * @return
     */
    public static FlowShowStatusEnum getByCode(String code) {
        for (FlowShowStatusEnum itemEnum : FlowShowStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (FlowShowStatusEnum itemEnum : FlowShowStatusEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }

}

