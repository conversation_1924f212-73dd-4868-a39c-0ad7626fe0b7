package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 活动方式[优惠券|免佣|返现]
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum ActivityMannerEnum {

    COUPON("1", "优惠券"),
    FREE("2", "免佣"),
    CASH("3", "现金"),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     *
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (ActivityMannerEnum itemEnum : ActivityMannerEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

