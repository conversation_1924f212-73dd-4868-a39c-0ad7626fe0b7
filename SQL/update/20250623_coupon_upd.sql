SET
FOREIGN_KEY_CHECKS=0;

ALTER TABLE `mkt_coupon`
    ADD COLUMN `start_time` datetime NULL DEFAULT NULL COMMENT '生效开始时间' AFTER `del_flag`;

ALTER TABLE `mkt_coupon`
    ADD COLUMN `end_time` datetime NULL DEFAULT NULL COMMENT '生效结束时间' AFTER `start_time`;

ALTER TABLE `mkt_coupon`
    ADD COLUMN `product_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '0=全部 1=独享 2=拼车' AFTER `end_time`;

ALTER TABLE `mkt_coupon`
    ADD COLUMN `shelves_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '0=下架 1=上架' AFTER `product_scope`;

ALTER TABLE `mkt_coupon` MODIFY COLUMN `margin` bigint NULL DEFAULT 0 COMMENT '余量' AFTER `total`;

ALTER TABLE `mkt_coupon_grant`
    ADD COLUMN `product_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '0=全部 1=独享 2=拼车' AFTER `del_flag`;

ALTER TABLE `mkt_coupon_grant`
    ADD COLUMN `source_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '来源类型：1=活动，2=定向发放' AFTER `product_scope`;

ALTER TABLE `mkt_coupon_grant`
    ADD COLUMN `source_id` bigint NULL DEFAULT NULL COMMENT '来源ID，如活动ID、发放表ID等' AFTER `source_type`;

ALTER TABLE `mkt_coupon_grant` MODIFY COLUMN `tenant_id` varchar (20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id' AFTER `id`;

ALTER TABLE `mkt_coupon_grant` MODIFY COLUMN `agent_id` bigint NULL DEFAULT NULL COMMENT '代理商ID' AFTER `tenant_id`;

ALTER TABLE `dev_xx_market`.`mkt_coupon_grant`
    ADD COLUMN `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '订单号' AFTER `source_id`;

CREATE TABLE `mkt_targeted_coupons`
(
    `id`             bigint                                                        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `title`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '放券标题',
    `coupon_id`      bigint                                                        NOT NULL DEFAULT 0 COMMENT '优惠卷ID',
    `coupon_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '优惠券名称',
    `per_user_limit` int                                                           NOT NULL COMMENT '单个用户，发放数量',
    `phone_numbers`  json                                                          NOT NULL COMMENT '发放的用户手机号',
    `coupon_type`    char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '优惠类型[DiscountTypeEnum],0:立减券;1:满减券',
    `status`         char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态，1：已发放，2：已撤销',
    `target`         char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      NOT NULL COMMENT '优惠对象[CouponTargetEnum],0:线路专属；1:全平台通用；2:城市专属',
    `quota`          bigint                                                        NOT NULL DEFAULT 0 COMMENT '额度,面值，单位分',
    `expire_time`    int NULL DEFAULT NULL COMMENT '过期时间(单位:s)',
    `start_time`     datetime NULL DEFAULT NULL COMMENT '生效开始时间',
    `end_time`       datetime NULL DEFAULT NULL COMMENT '生效结束时间',
    `create_by`      bigint NULL DEFAULT NULL COMMENT '创建者',
    `create_dept`    bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_by`      bigint NULL DEFAULT NULL COMMENT '更新者',
    `update_time`    datetime(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `del_flag`       char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `tenant_id`      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定向放券表' ROW_FORMAT = Dynamic;


SET
FOREIGN_KEY_CHECKS=1;