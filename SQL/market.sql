
DROP TABLE IF EXISTS `mkt_coupon`;
CREATE TABLE `mkt_coupon` (
                              `id` bigint NOT NULL COMMENT 'id',
                              `tenant_id` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户id',
                              `activity_id` bigint DEFAULT NULL COMMENT '活动id',
                              `agent_id` bigint DEFAULT NULL COMMENT '代理商id',
                              `coupon_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠卷编码',
                              `line_id` bigint DEFAULT NULL COMMENT '线路id',
                              `city_code` varchar(16) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市编码',
                              `activity_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠卷活动名称',
                              `name` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '活动名称',
                              `discount_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '优惠类型[DiscountTypeEnum]',
                              `quota` bigint DEFAULT '0' COMMENT '额度',
                              `total` bigint DEFAULT '0' COMMENT '总数',
                              `margin` bigint DEFAULT '0' COMMENT '余量',
                              `rule` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规则',
                              `expire_time` int DEFAULT NULL COMMENT '过期时间(单位:s)',
                              `max_num` bigint DEFAULT '1' COMMENT '最大领取数量',
                              `terms_of_use` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '使用条件 满 x 元可用 满减卷',
                              `paid_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '领取方式[PaidTypeEnum]',
                              `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '优惠券状态 [CouponTemplateStatusEnum]',
                              `target` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠对象[CouponTargetEnum]',
                              `source` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠券来源 [CouponSourceEnum]',
                              `create_by` bigint DEFAULT NULL COMMENT '创建者',
                              `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
                              `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                              `update_by` bigint DEFAULT NULL COMMENT '更新者',
                              `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                              `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='优惠券表';

DROP TABLE IF EXISTS `mkt_coupon_grant`;
CREATE TABLE `mkt_coupon_grant` (
                                    `id` bigint NOT NULL COMMENT 'id',
                                    `tenant_id` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户id',
                                    `passenger_id` bigint DEFAULT '0' COMMENT '乘客ID',
                                    `agent_id` bigint DEFAULT NULL COMMENT '代理商ID',
                                    `activity_id` bigint DEFAULT '0' COMMENT '活动ID',
                                    `coupon_id` bigint DEFAULT '0' COMMENT '优惠卷ID',
                                    `coupon_name` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠卷名',
                                    `line_id` bigint DEFAULT NULL COMMENT '线路ID',
                                    `city_code` varchar(16) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市编码',
                                    `coupon_code` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠卷编码',
                                    `order_id` bigint DEFAULT '0' COMMENT '订单ID',
                                    `discount_type` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '折扣方式',
                                    `quota` bigint DEFAULT '0' COMMENT '折扣额度',
                                    `paid_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '领取方式[PaidTypeEnum]',
                                    `paid_user_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '领取人类型',
                                    `using_status` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '使用状态[CouponStatusEnum]',
                                    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
                                    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
                                    `wiped_time` datetime DEFAULT NULL COMMENT '核销时间',
                                    `create_by` bigint DEFAULT NULL COMMENT '创建者',
                                    `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
                                    `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
                                    `update_by` bigint DEFAULT NULL COMMENT '更新者',
                                    `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
                                    `del_flag` char(1) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='优惠券发放表';